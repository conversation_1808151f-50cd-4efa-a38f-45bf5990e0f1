import 'dart:convert';
import 'dart:html' as html;
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';

class AuthService extends ChangeNotifier {
  static const String _baseUrl = 'https://connect.kingsch.at/api';
  static const String _authUrl = 'https://accounts.kingsch.at';
  static const String _clientId = '619b30ea-a682-47fb-b90f-5b8e780b89ca';
  static const List<String> _scopes = ['conference_calls'];
  
  AuthTokens? _tokens;
  KingsChatUserData? _userData;
  bool _isLoading = false;
  String? _error;

  // Getters
  AuthTokens? get tokens => _tokens;
  KingsChatUserData? get userData => _userData;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _tokens != null && !_tokens!.isExpired;
  UserModel? get currentUser => _userData?.profile.user;

  AuthService() {
    _loadStoredAuth();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Load stored authentication data
  Future<void> _loadStoredAuth() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final tokenData = prefs.getString('auth_tokens');
      final userData = prefs.getString('user_data');
      
      if (tokenData != null) {
        _tokens = AuthTokens.fromJson(jsonDecode(tokenData));
        
        // Check if token is expired
        if (_tokens!.isExpired) {
          await _refreshToken();
        }
      }
      
      if (userData != null) {
        _userData = KingsChatUserData.fromJson(jsonDecode(userData));
      }
      
      notifyListeners();
    } catch (e) {
      print('Error loading stored auth: $e');
    }
  }

  // Save authentication data
  Future<void> _saveAuth() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      if (_tokens != null) {
        await prefs.setString('auth_tokens', jsonEncode(_tokens!.toJson()));
      }
      
      if (_userData != null) {
        await prefs.setString('user_data', jsonEncode(_userData!.toJson()));
      }
    } catch (e) {
      print('Error saving auth: $e');
    }
  }

  // Start OAuth login process
  String getLoginUrl() {
    final redirectUri = '${html.window.location.origin}/auth/callback';
    
    final params = {
      'client_id': _clientId,
      'scopes': jsonEncode(_scopes),
      'redirect_uri': redirectUri,
      'response_type': 'token',
      'post_redirect': 'true',
    };
    
    final queryString = params.entries
        .map((e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');
    
    return '$_authUrl/?$queryString';
  }

  // Handle OAuth callback
  Future<bool> handleCallback(String accessToken, [String? refreshToken]) async {
    _setLoading(true);
    _setError(null);
    
    try {
      // Create tokens object
      _tokens = AuthTokens(
        accessToken: accessToken,
        refreshToken: refreshToken,
        expiresAt: DateTime.now().add(const Duration(hours: 1)).millisecondsSinceEpoch ~/ 1000,
      );
      
      // Fetch user profile
      final success = await _fetchUserProfile();
      
      if (success) {
        await _saveAuth();
        notifyListeners();
        return true;
      }
      
      return false;
    } catch (e) {
      _setError('Login failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Fetch user profile from API
  Future<bool> _fetchUserProfile() async {
    if (_tokens == null) return false;
    
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/profile'),
        headers: {
          'Authorization': 'Bearer ${_tokens!.accessToken}',
          'Content-Type': 'application/json',
        },
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        _userData = KingsChatUserData.fromJson(data);
        return true;
      } else {
        _setError('Failed to fetch user profile: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      _setError('Error fetching user profile: $e');
      return false;
    }
  }

  // Refresh access token
  Future<bool> _refreshToken() async {
    if (_tokens?.refreshToken == null) return false;
    
    try {
      // This would typically call your PHP backend to refresh the token
      // For now, we'll implement a basic refresh mechanism
      final response = await http.post(
        Uri.parse('${html.window.location.origin}/oauth_handler.php'),
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: {
          'action': 'force_refresh',
          'refresh_token': _tokens!.refreshToken!,
        },
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true && data['access_token'] != null) {
          _tokens = AuthTokens(
            accessToken: data['access_token'],
            refreshToken: _tokens!.refreshToken,
            expiresAt: data['expires_at'],
          );
          await _saveAuth();
          return true;
        }
      }
      
      return false;
    } catch (e) {
      print('Error refreshing token: $e');
      return false;
    }
  }

  // Logout
  Future<void> logout() async {
    _tokens = null;
    _userData = null;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_tokens');
    await prefs.remove('user_data');
    
    notifyListeners();
  }

  // Check if token needs refresh and refresh if needed
  Future<void> ensureValidToken() async {
    if (_tokens != null && _tokens!.needsRefresh()) {
      await _refreshToken();
    }
  }
}
