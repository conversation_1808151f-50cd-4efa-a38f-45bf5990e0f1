import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:html' as html;
import '../services/auth_service.dart';

class CallbackScreen extends StatefulWidget {
  const CallbackScreen({super.key});

  @override
  State<CallbackScreen> createState() => _CallbackScreenState();
}

class _CallbackScreenState extends State<CallbackScreen> {
  bool _isProcessing = true;
  String _status = 'Processing authentication...';

  @override
  void initState() {
    super.initState();
    _handleCallback();
  }

  Future<void> _handleCallback() async {
    try {
      // Get the current URL fragment
      final fragment = html.window.location.hash;
      
      if (fragment.isEmpty || !fragment.contains('access_token=')) {
        setState(() {
          _status = 'No authentication data found in URL';
          _isProcessing = false;
        });
        return;
      }

      // Parse the fragment to extract tokens
      final params = _parseFragment(fragment);
      final accessToken = params['access_token'];
      final refreshToken = params['refresh_token'];

      if (accessToken == null) {
        setState(() {
          _status = 'Access token not found';
          _isProcessing = false;
        });
        return;
      }

      setState(() {
        _status = 'Authenticating with KingsChat...';
      });

      // Handle the callback with the auth service
      final authService = Provider.of<AuthService>(context, listen: false);
      final success = await authService.handleCallback(accessToken, refreshToken);

      if (success) {
        setState(() {
          _status = 'Authentication successful! Redirecting...';
        });
        
        // Clear the URL fragment and redirect to dashboard
        html.window.history.replaceState(null, '', '/');
        
        // Navigate to dashboard
        if (mounted) {
          Navigator.of(context).pushReplacementNamed('/dashboard');
        }
      } else {
        setState(() {
          _status = 'Authentication failed. Please try again.';
          _isProcessing = false;
        });
      }
    } catch (e) {
      setState(() {
        _status = 'Error processing authentication: $e';
        _isProcessing = false;
      });
    }
  }

  Map<String, String> _parseFragment(String fragment) {
    final params = <String, String>{};
    
    // Remove the # at the beginning
    final cleanFragment = fragment.startsWith('#') ? fragment.substring(1) : fragment;
    
    // Split by & and parse key=value pairs
    for (final pair in cleanFragment.split('&')) {
      final parts = pair.split('=');
      if (parts.length == 2) {
        params[Uri.decodeComponent(parts[0])] = Uri.decodeComponent(parts[1]);
      }
    }
    
    return params;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF4A90E2),
              Color(0xFF357ABD),
            ],
          ),
        ),
        child: Center(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 400),
            margin: const EdgeInsets.all(24),
            child: Card(
              elevation: 8,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Loading indicator or status icon
                    if (_isProcessing)
                      const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4A90E2)),
                      )
                    else
                      Icon(
                        _status.contains('successful') 
                            ? Icons.check_circle_outline
                            : Icons.error_outline,
                        size: 64,
                        color: _status.contains('successful') 
                            ? Colors.green 
                            : Colors.red,
                      ),
                    
                    const SizedBox(height: 24),
                    
                    // Status text
                    Text(
                      _status,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2C3E50),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Action button (only show if not processing and not successful)
                    if (!_isProcessing && !_status.contains('successful'))
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pushReplacementNamed('/');
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF4A90E2),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text(
                            'Back to Login',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
