<?php
// Simple dashboard without token refresh logic to test
session_start();

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');

// Basic security headers
header("X-Frame-Options: DENY");
header("X-XSS-Protection: 1; mode=block");
header("X-Content-Type-Options: nosniff");

// Check if user is logged in (simplified check)
if (!isset($_SESSION['kc_access_token'])) {
    // Instead of redirecting, show a login message
    echo "<h1>Please Login First</h1>";
    echo "<p>You need to login to access the dashboard.</p>";
    echo "<p><a href='index.php'>Go to Login</a></p>";
    exit;
}

// Get user data from session
$userData = isset($_SESSION['kc_user']['profile']['user']) ? $_SESSION['kc_user']['profile']['user'] : null;
$userEmail = isset($_SESSION['kc_user']['profile']['email']['address']) ? $_SESSION['kc_user']['profile']['email']['address'] : null;
$userId = $userData['_id'] ?? null;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KingsBlast - Simple Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto py-6 px-4">
        <h1 class="text-3xl font-bold text-gray-900 mb-6">Simple Dashboard Test</h1>
        
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">User Information</h2>
            <div class="space-y-2">
                <p><strong>Name:</strong> <?php echo isset($userData['name']) ? htmlspecialchars($userData['name']) : 'Not available'; ?></p>
                <p><strong>Username:</strong> <?php echo isset($userData['username']) ? htmlspecialchars($userData['username']) : 'Not available'; ?></p>
                <p><strong>Email:</strong> <?php echo $userEmail ? htmlspecialchars($userEmail) : 'Not available'; ?></p>
                <p><strong>User ID:</strong> <?php echo $userId ? htmlspecialchars($userId) : 'Not available'; ?></p>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Session Information</h2>
            <div class="space-y-2">
                <p><strong>Session ID:</strong> <?php echo session_id(); ?></p>
                <p><strong>Token exists:</strong> <?php echo isset($_SESSION['kc_access_token']) ? 'Yes' : 'No'; ?></p>
                <?php if (isset($_SESSION['kc_access_token'])): ?>
                <p><strong>Token length:</strong> <?php echo strlen($_SESSION['kc_access_token']); ?></p>
                <p><strong>Token expires at:</strong> <?php echo $_SESSION['kc_token_expires_at'] ?? 'Not set'; ?></p>
                <p><strong>Current time:</strong> <?php echo time(); ?></p>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">Actions</h2>
            <div class="space-x-4">
                <a href="dashboard.php" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Try Full Dashboard</a>
                <a href="logout.php" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">Logout</a>
            </div>
        </div>
    </div>
</body>
</html>
