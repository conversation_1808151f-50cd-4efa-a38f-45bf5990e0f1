{"name": "kingschat-web-sdk", "version": "0.1.0", "description": "KingsChat Web SDK", "license": "ISC", "repository": "kingschat/kingschat-web-sdk", "main": "dist/index.js", "author": {"name": "kingschat", "email": "<EMAIL>", "url": "https://github.com/kingschat"}, "files": ["dist"], "types": "dist/ts/index.d.ts", "scripts": {"test": "jest", "coverage": "npm test -- --coverage", "postcoverage": "opn coverage/lcov-report/index.html", "type-check": "tsc --noEmit", "lint": "eslint . --ext js,ts", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "build": "tsc --emitDeclarationOnly && babel src -d dist -x .js,.ts --copy-files", "postbuild": "rimraf \"dist/**/*{test,test.d}.{js,ts}\"", "format": "npm run format:prettier ", "format:prettier": "prettier --config .prettierrc \"src/**/*.{js,ts}\" --write", "format-test": "npm run format-test:prettier", "format-test:prettier": "prettier --config .prettierrc \"src/**/*.{js,ts}\" -l"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix --ext js,ts", "git add"]}, "keywords": ["kingschat", "sdk", "web", "login"], "dependencies": {}, "devDependencies": {"@babel/cli": "7.4.4", "@babel/core": "7.4.5", "@babel/plugin-proposal-class-properties": "7.3.4", "@babel/preset-env": "7.3.4", "@babel/preset-typescript": "7.3.3", "@types/jest": "24.0.12", "@typescript-eslint/eslint-plugin": "1.9.0", "@typescript-eslint/parser": "1.9.0", "babel-eslint": "10.0.1", "babel-jest": "24.1.0", "eslint": "5.15.1", "eslint-config-airbnb-base": "13.1.0", "eslint-config-prettier": "4.1.0", "eslint-plugin-import": "2.16.0", "eslint-plugin-prettier": "3.0.1", "husky": "1.3.1", "jest": "24.1.0", "lint-staged": "8.1.5", "opn-cli": "4.0.0", "prettier": "1.16.4", "rimraf": "2.6.3", "typescript": "3.4.5"}}