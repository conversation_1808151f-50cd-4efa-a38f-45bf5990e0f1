import { loginOptionsI, sendMessageOptionsI, refreshAuthenticationTokenOptionsI } from '../interfaces';
export declare const validEnvironment: (environment?: "dev" | "staging" | "prod" | undefined) => void;
export declare const validLoginOptions: (loginOptions: loginOptionsI) => void;
export declare const validRefreshAuthenticationTokenOptions: (refreshAuthenticationTokenOptions: refreshAuthenticationTokenOptionsI) => void;
export declare const validSendMessageOptions: (sendMessageOptions: sendMessageOptionsI) => void;
declare const _default: {
    validEnvironment: (environment?: "dev" | "staging" | "prod" | undefined) => void;
    validLoginOptions: (loginOptions: loginOptionsI) => void;
    validRefreshAuthenticationTokenOptions: (refreshAuthenticationTokenOptions: refreshAuthenticationTokenOptionsI) => void;
    validSendMessageOptions: (sendMessageOptions: sendMessageOptionsI) => void;
};
export default _default;
