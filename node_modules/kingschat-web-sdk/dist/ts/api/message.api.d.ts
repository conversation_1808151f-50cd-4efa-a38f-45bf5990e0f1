import { sendMessageOptionsI } from '../interfaces';
export declare const sendMessageRequest: ({ sendMessageOptions, environment, }: {
    sendMessageOptions: sendMessageOptionsI;
    environment?: "dev" | "staging" | "prod" | undefined;
}) => Promise<string>;
declare const _default: {
    sendMessageRequest: ({ sendMessageOptions, environment, }: {
        sendMessageOptions: sendMessageOptionsI;
        environment?: "dev" | "staging" | "prod" | undefined;
    }) => Promise<string>;
};
export default _default;
