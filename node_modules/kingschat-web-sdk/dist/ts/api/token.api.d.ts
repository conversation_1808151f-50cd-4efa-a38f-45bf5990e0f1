import { authenticationTokenResponseI, refreshAuthenticationTokenOptionsI } from '../interfaces';
export declare const refreshAuthenticationTokenRequest: ({ refreshAuthenticationTokenOptions, environment, }: {
    refreshAuthenticationTokenOptions: refreshAuthenticationTokenOptionsI;
    environment?: "dev" | "staging" | "prod" | undefined;
}) => Promise<authenticationTokenResponseI>;
declare const _default: {
    refreshAuthenticationTokenRequest: ({ refreshAuthenticationTokenOptions, environment, }: {
        refreshAuthenticationTokenOptions: refreshAuthenticationTokenOptionsI;
        environment?: "dev" | "staging" | "prod" | undefined;
    }) => Promise<authenticationTokenResponseI>;
};
export default _default;
