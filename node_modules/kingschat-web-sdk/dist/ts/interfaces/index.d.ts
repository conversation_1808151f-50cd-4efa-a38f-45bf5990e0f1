export declare type env = 'dev' | 'staging' | 'prod';
export interface loginOptionsI {
    scopes: Array<string>;
    clientId: string;
}
export interface authenticationTokenResponseI {
    accessToken: string;
    expiresInMillis: number;
    refreshToken: string;
}
export interface refreshAuthenticationTokenOptionsI {
    clientId: string;
    refreshToken: string;
}
export interface sendMessageOptionsI {
    message: string;
    userIdentifier: string;
    accessToken: string;
}
export interface windowAreaI {
    width: number;
    height: number;
    left?: number;
    top?: number;
}
