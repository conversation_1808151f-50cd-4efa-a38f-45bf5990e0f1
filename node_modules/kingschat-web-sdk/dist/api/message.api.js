"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.sendMessageRequest = void 0;

var _constants = require("../constants");

const sendMessageRequest = ({
  sendMessageOptions,
  environment = 'prod'
}) => {
  return fetch(`${_constants.kingsChatApiPaths[environment]}/api/users/${sendMessageOptions.userIdentifier}/new_message`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${sendMessageOptions.accessToken}`
    },
    body: JSON.stringify({
      message: {
        body: {
          text: {
            body: sendMessageOptions.message
          }
        }
      }
    })
  }).then(response => {
    if (response.ok) {
      return response.json();
    }

    return Promise.reject(Error('error'));
  }).catch(error => {
    return Promise.reject(Error(error.message));
  });
};

exports.sendMessageRequest = sendMessageRequest;
var _default = {
  sendMessageRequest
};
exports.default = _default;