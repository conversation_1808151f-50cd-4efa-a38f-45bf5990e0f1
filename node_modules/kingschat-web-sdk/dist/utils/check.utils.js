"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.validSendMessageOptions = exports.validRefreshAuthenticationTokenOptions = exports.validLoginOptions = exports.validEnvironment = void 0;

const validEnvironment = environment => {
  if (environment) {
    const allowedEnvironments = ['dev', 'staging', 'prod'];

    if (typeof environment !== 'string' || !allowedEnvironments.includes(environment)) {
      throw Error('environment is invalid');
    }
  }
};

exports.validEnvironment = validEnvironment;

const validLoginOptions = loginOptions => {
  if (!loginOptions) {
    throw Error('loginOptions are not defined!');
  }

  if (!loginOptions.scopes) {
    throw Error('scopes are not defined!');
  } else if (!Array.isArray(loginOptions.scopes)) {
    throw Error(`scopes are type of ${typeof loginOptions.scopes} instead of Array`);
  }

  if (!loginOptions.clientId) {
    throw Error('clientId is not defined!');
  } else if (typeof loginOptions.clientId !== 'string') {
    throw Error(`clientId is type of ${typeof loginOptions.clientId} instead of string`);
  }
};

exports.validLoginOptions = validLoginOptions;

const validRefreshAuthenticationTokenOptions = refreshAuthenticationTokenOptions => {
  if (!refreshAuthenticationTokenOptions) {
    throw Error('refreshAuthenticationTokenOptions are not defined!');
  }

  if (!refreshAuthenticationTokenOptions.clientId) {
    throw Error('clientId is not defined!');
  } else if (typeof refreshAuthenticationTokenOptions.clientId !== 'string') {
    throw Error(`clientId is type of ${typeof refreshAuthenticationTokenOptions.clientId} instead of string`);
  }

  if (!refreshAuthenticationTokenOptions.refreshToken) {
    throw Error('refreshToken is not defined!');
  } else if (typeof refreshAuthenticationTokenOptions.refreshToken !== 'string') {
    throw Error(`refreshToken is type of ${typeof refreshAuthenticationTokenOptions.refreshToken} instead of string`);
  }
};

exports.validRefreshAuthenticationTokenOptions = validRefreshAuthenticationTokenOptions;

const validSendMessageOptions = sendMessageOptions => {
  if (!sendMessageOptions) {
    throw Error('sendMessageOptions are not defined!');
  }

  if (!sendMessageOptions.message) {
    throw Error('message is not defined!');
  } else if (typeof sendMessageOptions.message !== 'string') {
    throw Error(`message is type of ${typeof sendMessageOptions.message} instead of string`);
  }

  if (!sendMessageOptions.accessToken) {
    throw Error('accessToken is not defined!');
  } else if (typeof sendMessageOptions.accessToken !== 'string') {
    throw Error(`accessToken is type of ${typeof sendMessageOptions.accessToken} instead of string`);
  }

  if (!sendMessageOptions.userIdentifier) {
    throw Error('userIdentifier is not defined!');
  } else if (typeof sendMessageOptions.userIdentifier !== 'string') {
    throw Error(`userIdentifier is type of ${typeof sendMessageOptions.userIdentifier} instead of string`);
  }
};

exports.validSendMessageOptions = validSendMessageOptions;
var _default = {
  validEnvironment,
  validLoginOptions,
  validRefreshAuthenticationTokenOptions,
  validSendMessageOptions
};
exports.default = _default;