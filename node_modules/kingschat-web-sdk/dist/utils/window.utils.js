"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.loginWindow = void 0;

var _constants = require("../constants");

var _parse = require("./parse.utils");

/* global window */
function newWindowOptions() {
  if (!window) throw Error('No window defined');
  const windowArea = {
    width: Math.min(Math.floor(window.outerWidth * 0.9), 950),
    height: Math.min(Math.floor(window.outerHeight * 0.9), 600)
  };
  windowArea.left = Math.floor(window.screenX + (window.outerWidth - windowArea.width) / 2);
  windowArea.top = Math.floor(window.screenY + (window.outerHeight - windowArea.height) / 8);
  return `toolbar=0,scrollbars=1,status=1,resizable=1,location=1,menuBar=0,
    width=${windowArea.width},height=${windowArea.height},
    left=${windowArea.left},top=${windowArea.top}`;
}

function newWindowUrl({
  myUrl,
  options
}) {
  const url = new URL(myUrl.href);
  url.searchParams.append('client_id', options.clientId || '');
  url.searchParams.append('scopes', (0, _parse.parseScopesArrayToString)(options.scopes));
  url.searchParams.append('redirect_uri', window.location.origin);
  url.searchParams.append('post_message', '1');
  return url;
}

const loginWindow = (myUrl, options) => {
  const windowOptions = newWindowOptions();
  const windowURL = newWindowUrl({
    myUrl,
    options
  });
  const authWindow = window.open(windowURL.href, '_blank', windowOptions);

  if (!authWindow) {
    return Promise.reject(Error('You have to enable popups to show login window'));
  } // Listen to message from child window


  return new Promise((resolve, reject) => {
    const listener = msg => {
      /* Ignore self messages like setImmediate - Messages from other windows won't have source for security reasons */
      if (msg.source === window) {
        return;
      }

      if (!_constants.allowedResponseOrigins.includes(msg.origin)) {
        authWindow.close();
        reject(Error('Not allowed message origin'));
      }

      if (msg.data) {
        authWindow.close();

        if (msg.data.error) {
          reject(Error(msg.data.error));
        } else {
          resolve(msg.data);
        }
      } else {
        reject(Error('Bad Request'));
      }
    };

    window.addEventListener('message', listener, false);
    const interval = setInterval(() => {
      if (!authWindow.window) {
        window.removeEventListener('message', listener, false);
        clearInterval(interval);
        reject(Error('User closed window before allowing access'));
      }
    }, 350);
  });
};

exports.loginWindow = loginWindow;
var _default = {
  loginWindow
};
exports.default = _default;