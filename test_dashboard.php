<?php
// Simple test to check what's causing the redirect loop
session_start();

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');

echo "<h1>Dashboard Debug Test</h1>";

// Check session
echo "<h2>Session Status:</h2>";
echo "<pre>";
echo "Session ID: " . session_id() . "\n";
echo "Session status: " . session_status() . "\n";
echo "kc_access_token exists: " . (isset($_SESSION['kc_access_token']) ? 'YES' : 'NO') . "\n";

if (isset($_SESSION['kc_access_token'])) {
    echo "Token length: " . strlen($_SESSION['kc_access_token']) . "\n";
    echo "Token expires at: " . ($_SESSION['kc_token_expires_at'] ?? 'Not set') . "\n";
    echo "Current time: " . time() . "\n";
}

echo "\nFull session data:\n";
print_r($_SESSION);
echo "</pre>";

// Check if token refresh functions exist
echo "<h2>Function Check:</h2>";
echo "<pre>";
echo "needsTokenRefresh function exists: " . (function_exists('needsTokenRefresh') ? 'YES' : 'NO') . "\n";
echo "refreshKingsChatToken function exists: " . (function_exists('refreshKingsChatToken') ? 'YES' : 'NO') . "\n";
echo "</pre>";

// Try to include the token refresh file
echo "<h2>Include Test:</h2>";
echo "<pre>";
try {
    require_once 'token_refresh.php';
    echo "token_refresh.php included successfully\n";
    
    if (function_exists('needsTokenRefresh')) {
        $needsRefresh = needsTokenRefresh();
        echo "needsTokenRefresh() result: " . ($needsRefresh ? 'TRUE' : 'FALSE') . "\n";
    }
} catch (Exception $e) {
    echo "Error including token_refresh.php: " . $e->getMessage() . "\n";
}
echo "</pre>";

// Check welcome message file
echo "<h2>Welcome Message Test:</h2>";
echo "<pre>";
try {
    require_once 'send_welcome_message.php';
    echo "send_welcome_message.php included successfully\n";
} catch (Exception $e) {
    echo "Error including send_welcome_message.php: " . $e->getMessage() . "\n";
}
echo "</pre>";

echo "<h2>Test Complete</h2>";
echo "<p><a href='dashboard.php'>Try Dashboard</a></p>";
?>
